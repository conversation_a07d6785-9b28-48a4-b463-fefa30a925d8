package com.investment.service;

import com.investment.api.model.CreateWatchListRequest;
import com.investment.api.model.UpdateWatchListRequest;
import com.investment.database.DatabaseManager;
import com.investment.model.WatchListItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Service for managing watch list operations.
 * Follows garbage-free patterns for low-latency trading systems.
 */
@Service
public class WatchListService {

    private static final Logger logger = LoggerFactory.getLogger(WatchListService.class);

    private final DatabaseManager databaseManager;

    public WatchListService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }

    /**
     * Create a new watch list item.
     */
    public WatchListItem createWatchListItem(CreateWatchListRequest request) throws SQLException {
        logger.info("Creating new watch list item: {}", request);

        // Validate that the symbol exists in instruments table
        if (!databaseManager.symbolExists(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol not found in instruments: " + request.getSymbol());
        }

        // Check if symbol already exists in watch list
        if (databaseManager.symbolExistsInWatchList(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol already exists in watch list: " + request.getSymbol());
        }

        WatchListItem item = request.toWatchListItem();

        Long id = databaseManager.createWatchListItem(
                item.getDisplayIndex(),
                item.getSymbol(),
                java.sql.Date.valueOf(item.getStartDate()),
                item.getRemarks()
        );

        item.setId(id);

        logger.info("Created watch list item with ID: {}", item.getId());
        return item;
    }

    /**
     * Get a watch list item by ID.
     */
    public Optional<WatchListItem> getWatchListItemById(Long id) throws SQLException {
        logger.debug("Retrieving watch list item by ID: {}", id);

        Map<String, Object> data = databaseManager.getWatchListItemById(id);
        if (data != null) {
            return Optional.of(mapDataToWatchListItem(data));
        }

        return Optional.empty();
    }

    /**
     * Get all watch list items ordered by display index.
     */
    public List<WatchListItem> getAllWatchListItems() throws SQLException {
        logger.debug("Retrieving all watch list items");

        List<Map<String, Object>> data = databaseManager.getWatchListItems();
        List<WatchListItem> items = new ArrayList<>();

        for (Map<String, Object> row : data) {
            items.add(mapDataToWatchListItem(row));
        }

        logger.debug("Retrieved {} watch list items", items.size());
        return items;
    }

    /**
     * Update a watch list item.
     */
    public WatchListItem updateWatchListItem(Long id, UpdateWatchListRequest request) throws SQLException {
        logger.info("Updating watch list item ID: {} with request: {}", id, request);

        if (!request.hasUpdates()) {
            throw new IllegalArgumentException("No update fields provided");
        }

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();

        // Update fields if provided
        if (request.getDisplayIndex() != null) {
            item.setDisplayIndex(request.getDisplayIndex());
        }

        if (request.getRemarks() != null) {
            item.setRemarks(request.getRemarks());
        }

        if (request.hasPerformanceUpdates()) {
            item.updatePerformance(
                    request.getOneMonthPerf(),
                    request.getThreeMonthPerf(),
                    request.getSixMonthPerf()
            );
        }

        // Save to database
        databaseManager.updateWatchListItem(
                item.getId(),
                item.getDisplayIndex(),
                item.getRemarks(),
                item.getOneMonthPerf(),
                item.getThreeMonthPerf(),
                item.getSixMonthPerf()
        );

        logger.info("Updated watch list item ID: {}", id);
        return item;
    }

    /**
     * Update performance metrics for a watch list item.
     */
    public WatchListItem updateWatchListPerformance(Long id, BigDecimal oneMonthPerf, 
                                                   BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) throws SQLException {
        logger.info("Updating performance for watch list item ID: {}", id);

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();
        item.updatePerformance(oneMonthPerf, threeMonthPerf, sixMonthPerf);

        // Save to database
        databaseManager.updateWatchListPerformance(id, oneMonthPerf, threeMonthPerf, sixMonthPerf);

        logger.info("Updated performance for watch list item ID: {}", id);
        return item;
    }

    /**
     * Delete a watch list item.
     */
    public boolean deleteWatchListItem(Long id) throws SQLException {
        logger.info("Deleting watch list item ID: {}", id);

        boolean deleted = databaseManager.deleteWatchListItem(id);

        if (deleted) {
            logger.info("Deleted watch list item ID: {}", id);
        } else {
            logger.warn("Watch list item not found for deletion: {}", id);
        }

        return deleted;
    }

    /**
     * Bulk reorder watch list items.
     */
    public void reorderWatchListItems(Map<Long, Integer> idToIndexMap) throws SQLException {
        logger.info("Reordering {} watch list items", idToIndexMap.size());

        // Validate all IDs exist
        for (Long id : idToIndexMap.keySet()) {
            if (getWatchListItemById(id).isEmpty()) {
                throw new IllegalArgumentException("Watch list item not found: " + id);
            }
        }

        // Perform bulk update
        databaseManager.updateWatchListDisplayIndexes(idToIndexMap);

        logger.info("Successfully reordered {} watch list items", idToIndexMap.size());
    }

    /**
     * Calculate and update performance metrics for all watch list items.
     * This method would typically be called by a scheduled job.
     */
    public void calculateAndUpdateAllPerformance() throws SQLException {
        logger.info("Calculating performance for all watch list items");

        List<WatchListItem> items = getAllWatchListItems();
        int updatedCount = 0;

        for (WatchListItem item : items) {
            try {
                // Calculate performance based on OHLCV data
                PerformanceMetrics metrics = calculatePerformanceMetrics(item.getSymbol(), item.getStartDate());
                
                if (metrics.hasData()) {
                    databaseManager.updateWatchListPerformance(
                            item.getId(),
                            metrics.getOneMonthPerf(),
                            metrics.getThreeMonthPerf(),
                            metrics.getSixMonthPerf()
                    );
                    updatedCount++;
                }
            } catch (Exception e) {
                logger.warn("Failed to calculate performance for symbol: {}", item.getSymbol(), e);
            }
        }

        logger.info("Updated performance for {} out of {} watch list items", updatedCount, items.size());
    }

    /**
     * Calculate performance metrics for a symbol from start date.
     */
    private PerformanceMetrics calculatePerformanceMetrics(String symbol, LocalDate startDate) {
        // This is a placeholder implementation
        // In a real system, this would query OHLCV data and calculate actual performance
        logger.debug("Calculating performance metrics for {} from {}", symbol, startDate);
        
        // For now, return empty metrics
        return new PerformanceMetrics();
    }

    /**
     * Map database data to WatchListItem object.
     */
    private WatchListItem mapDataToWatchListItem(Map<String, Object> data) {
        WatchListItem item = new WatchListItem();

        item.setId((Long) data.get("id"));
        item.setDisplayIndex((Integer) data.get("display_index"));
        item.setSymbol((String) data.get("symbol"));
        
        java.sql.Date startDate = (java.sql.Date) data.get("start_date");
        if (startDate != null) {
            item.setStartDate(startDate.toLocalDate());
        }
        
        item.setRemarks((String) data.get("remarks"));
        item.setOneMonthPerf((BigDecimal) data.get("one_mo_perf"));
        item.setThreeMonthPerf((BigDecimal) data.get("three_mo_perf"));
        item.setSixMonthPerf((BigDecimal) data.get("six_mo_perf"));

        Timestamp createdTimestamp = (Timestamp) data.get("created_date");
        if (createdTimestamp != null) {
            item.setCreatedDate(createdTimestamp.toLocalDateTime());
        }

        Timestamp updatedTimestamp = (Timestamp) data.get("updated_date");
        if (updatedTimestamp != null) {
            item.setUpdatedDate(updatedTimestamp.toLocalDateTime());
        }

        return item;
    }

    /**
     * Helper class for performance metrics calculation.
     */
    private static class PerformanceMetrics {
        private BigDecimal oneMonthPerf;
        private BigDecimal threeMonthPerf;
        private BigDecimal sixMonthPerf;

        public boolean hasData() {
            return oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
        }

        public BigDecimal getOneMonthPerf() { return oneMonthPerf; }
        public BigDecimal getThreeMonthPerf() { return threeMonthPerf; }
        public BigDecimal getSixMonthPerf() { return sixMonthPerf; }
    }
}
