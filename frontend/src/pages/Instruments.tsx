import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import { Search as SearchIcon, Refresh as RefreshIcon } from '@mui/icons-material';

import { InstrumentService } from '../services/api/instrumentService';
import { Instrument } from '../types/api';

const Instruments: React.FC = () => {
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [totalInstruments, setTotalInstruments] = useState(0);

  useEffect(() => {
    loadInstruments();
  }, [page]);

  const loadInstruments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await InstrumentService.getInstruments(page, 50, 'marketCap', 'desc');
      setInstruments(response.data || []);
      setTotalInstruments(response.data?.length || 0);
    } catch (err: any) {
      console.error('Error loading instruments:', err);
      setError('Failed to load instruments');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadInstruments();
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await InstrumentService.searchInstruments(searchQuery);
      setInstruments(response.data || []);
    } catch (err: any) {
      console.error('Error searching instruments:', err);
      setError('Failed to search instruments');
    } finally {
      setLoading(false);
    }
  };

  const formatMarketCap = (marketCap?: number) => {
    if (!marketCap) return 'N/A';
    
    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    } else {
      return `$${marketCap.toLocaleString()}`;
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Financial Instruments
      </Typography>

      {/* Search and Actions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search instruments"
                placeholder="Enter symbol or company name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="contained"
                onClick={handleSearch}
                fullWidth
                startIcon={<SearchIcon />}
              >
                Search
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                onClick={loadInstruments}
                fullWidth
                startIcon={<RefreshIcon />}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Loading */}
      {loading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      )}

      {/* Instruments Table */}
      {!loading && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><strong>Symbol</strong></TableCell>
                <TableCell><strong>Company Name</strong></TableCell>
                <TableCell><strong>Exchange</strong></TableCell>
                <TableCell><strong>Sector</strong></TableCell>
                <TableCell><strong>Industry</strong></TableCell>
                <TableCell align="right"><strong>Market Cap</strong></TableCell>
                <TableCell><strong>Updated</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {instruments.map((instrument) => (
                <TableRow key={instrument.symbol} hover>
                  <TableCell>
                    <Chip
                      label={instrument.symbol}
                      variant="outlined"
                      size="small"
                      sx={{ fontWeight: 600 }}
                    />
                  </TableCell>
                  <TableCell>{instrument.name}</TableCell>
                  <TableCell>{instrument.type}</TableCell>
                  <TableCell>{instrument.sector || 'N/A'}</TableCell>
                  <TableCell>{instrument.industry || 'N/A'}</TableCell>
                  <TableCell align="right">
                    {formatMarketCap(instrument.marketCap)}
                  </TableCell>
                  <TableCell>
                    {instrument.country || 'N/A'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* No Results */}
      {!loading && instruments.length === 0 && (
        <Box textAlign="center" p={4}>
          <Typography variant="h6" color="text.secondary">
            No instruments found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search criteria or refresh the data
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Instruments;
