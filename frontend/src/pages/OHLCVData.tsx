import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Breadcrumbs,
  Link,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer } from 'recharts';

import { OHLCVService } from '../services/api/ohlcvService';
import { OHLCV } from '../types/api';

const OHLCVData: React.FC = () => {
  const [symbol, setSymbol] = useState('');
  const [ohlcvData, setOhlcvData] = useState<OHLCV[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!symbol.trim()) {
      setError('Please enter a symbol');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await OHLCVService.getOHLCVData(symbol.toUpperCase());
      setOhlcvData(response.data || []);
      
      if (!response.data || response.data.length === 0) {
        setError(`No OHLCV data found for symbol: ${symbol.toUpperCase()}`);
      }
    } catch (err: any) {
      console.error('Error loading OHLCV data:', err);
      setError(`Failed to load OHLCV data for ${symbol.toUpperCase()}`);
    } finally {
      setLoading(false);
    }
  };

  // Prepare chart data
  const chartData = ohlcvData.map(item => ({
    date: new Date(item.date).toLocaleDateString(),
    close: item.close,
    volume: item.volume,
    bollingerUpper: item.bollingerUpper,
    bollingerMiddle: item.bollingerMiddle,
    bollingerLower: item.bollingerLower,
  })).slice(-60); // Show last 60 days

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        OHLCV Data Analysis
      </Typography>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Stock Symbol"
                placeholder="Enter symbol (e.g., AAPL, MSFT)"
                value={symbol}
                onChange={(e) => setSymbol(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="contained"
                onClick={handleSearch}
                fullWidth
                startIcon={<SearchIcon />}
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Search'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Loading */}
      {loading && (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      )}

      {/* Chart */}
      {!loading && ohlcvData.length > 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Price Chart - {symbol.toUpperCase()}
                </Typography>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="close" 
                      stroke="#1976d2" 
                      strokeWidth={2}
                      name="Close Price"
                    />
                    {chartData.some(d => d.bollingerUpper) && (
                      <>
                        <Line 
                          type="monotone" 
                          dataKey="bollingerUpper" 
                          stroke="#ff9800" 
                          strokeDasharray="5 5"
                          name="Bollinger Upper"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="bollingerMiddle" 
                          stroke="#4caf50" 
                          strokeDasharray="5 5"
                          name="Bollinger Middle"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="bollingerLower" 
                          stroke="#f44336" 
                          strokeDasharray="5 5"
                          name="Bollinger Lower"
                        />
                      </>
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Summary Statistics */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Summary Statistics
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Data Points
                    </Typography>
                    <Typography variant="h6">
                      {ohlcvData.length}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Latest Close
                    </Typography>
                    <Typography variant="h6">
                      ${ohlcvData[ohlcvData.length - 1]?.close.toFixed(2)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      52W High
                    </Typography>
                    <Typography variant="h6">
                      ${Math.max(...ohlcvData.map(d => d.high)).toFixed(2)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      52W Low
                    </Typography>
                    <Typography variant="h6">
                      ${Math.min(...ohlcvData.map(d => d.low)).toFixed(2)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Technical Indicators
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {ohlcvData.some(d => d.bollingerUpper) 
                    ? 'Bollinger Bands data available' 
                    : 'No technical indicators calculated'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {ohlcvData.some(d => d.dmiAdx) 
                    ? 'DMI indicators available' 
                    : 'DMI indicators not calculated'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default OHLCVData;
