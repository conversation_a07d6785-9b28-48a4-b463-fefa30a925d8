// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

// Financial Instrument
export interface Instrument {
  symbol: string;
  name: string;
  type: string; // InstrumentType enum as string
  marketCap?: number;
  country?: string;
  ipoYear?: number;
  sector?: string;
  industry?: string;
}

// OHLCV Data
export interface OHLCV {
  id: number;
  symbol: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjustedClose: number;
  // Technical Indicators
  bollingerMiddle?: number;
  bollingerStdDev?: number;
  bollingerUpper?: number;
  bollingerLower?: number;
  dmiPlusDi?: number;
  dmiMinusDi?: number;
  dmiDx?: number;
  dmiAdx?: number;
}

// Position
export interface Position {
  id: number;
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice?: number;
  marketValue?: number;
  unrealizedPnl?: number;
  unrealizedPnlPercent?: number;
  entryDate: string;
  lastTradeDate?: string;
  stopLoss?: number;
  takeProfit?: number;
  riskAmount?: number;
  positionSize?: number;
  bollingerSignal?: string;
  createdAt: string;
  updatedAt: string;
}

// Watch List Item
export interface WatchListItem {
  id: number;
  symbol: string;
  targetPrice?: number;
  notes?: string;
  alertEnabled: boolean;
  oneMonthPerformance?: number;
  threeMonthPerformance?: number;
  sixMonthPerformance?: number;
  createdAt: string;
  updatedAt: string;
}

// Process Information
export interface ProcessInfo {
  processId: string;
  processType: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  progress?: number;
  message?: string;
  metadata?: Record<string, any>;
}

// Technical Indicator Calculation Request
export interface TechnicalIndicatorRequest {
  period?: number;
  stdDevMultiplier?: number;
  minDataPoints?: number;
  startIndex?: number;
  endIndex?: number;
  maxSymbols?: number;
  symbols?: string[];
  dryRun?: boolean;
  mode?: 'INCREMENTAL' | 'FULL_RECALCULATION' | 'SKIP_EXISTING';
}

// Refresh Request
export interface RefreshAllRequest {
  dryRun: boolean;
  maxSymbols?: number;
  skipExisting: boolean;
  startIndex?: number;
  endIndex?: number;
}

// Validation Request
export interface ValidationRequest {
  dryRun: boolean;
  maxSymbols?: number;
  removeInvalidSymbols: boolean;
  validateMarketCap: boolean;
  minMarketCap?: number;
}

// CSV Upload Response
export interface CsvUploadResponse {
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  validationErrors: string[];
  summary: string;
}

// Sync Response
export interface SyncResponse {
  totalSecInstruments: number;
  newInstruments: number;
  existingInstruments: number;
  errors: number;
  summary: string;
}

// Chart Data Point
export interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  bollingerUpper?: number;
  bollingerMiddle?: number;
  bollingerLower?: number;
}
